package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.dto.valueAdded.*;
import com.bxm.customer.domain.enums.ValueAddedBatchOperationType;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.enums.ExportOperationType;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.config.BatchOperationConfig;
import com.bxm.customer.service.IBatchValueAddedOperationService;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 批量操作服务实现类
 *
 * 提供增值交付单的各种批量操作功能实现
 * 包括批量确认、提交、关闭、处理异常、驳回、退回等操作
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Slf4j
@Service
public class ValueAddedBatchValueAddedOperationServiceImpl implements IBatchValueAddedOperationService {

    @Autowired
    private IValueAddedDeliveryOrderService valueAddedDeliveryOrderService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private BatchOperationConfig batchOperationConfig;

    @Override
    public BatchOperationResultDTO executeBatchOperation(BatchOperationRequestDTO request) {
        log.info("Starting batch operation: {}, order count: {}",
                request.getOperationType().getDescription(), request.getOrderCount());

        long startTime = System.currentTimeMillis();
        String startTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 验证请求参数
        validateBatchOperationRequest(request);

        // 批量查询交付单
        List<ValueAddedDeliveryOrder> orders = batchQueryOrders(request.getDeliveryOrderNos());

        // 执行批量操作
        BatchOperationResult result = processBatchOperation(orders, request);

        // 缓存异常数据
        String batchNo = null;
        if (!result.getErrors().isEmpty()) {
            batchNo = UUID.randomUUID().toString().replaceAll("-", "");
            cacheErrorData(batchNo, result.getErrors());
        }

        long endTime = System.currentTimeMillis();
        String endTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 构建返回结果
        BatchOperationResultDTO resultDTO = BatchOperationResultDTO.builder()
                .batchNo(batchNo)
                .operationDescription(request.getOperationDescription())
                .totalCount(orders.size())
                .successCount(result.getSuccessOrderNos().size())
                .errorCount(result.getErrors().size())
                .successOrderNos(result.getSuccessOrderNos())
                .errorOrderNos(result.getErrors().stream()
                        .map(BatchOperationErrorDTO::getDeliveryOrderNo)
                        .collect(Collectors.toList()))
                .startTime(startTimeStr)
                .endTime(endTimeStr)
                .duration(endTime - startTime)
                .hasErrors(!result.getErrors().isEmpty())
                .build();

        log.info("Batch operation completed: {}, total: {}, success: {}, error: {}, duration: {}ms",
                request.getOperationType().getDescription(),
                resultDTO.getTotalCount(), resultDTO.getSuccessCount(),
                resultDTO.getErrorCount(), resultDTO.getDuration());

        return resultDTO;
    }

    @Override
    public List<BatchOperationErrorDTO> getBatchOperationErrors(String batchNo) {
        if (batchNo == null || batchNo.trim().isEmpty()) {
            throw new IllegalArgumentException("批次号不能为空");
        }

        List<BatchOperationErrorDTO> errorList = redisService.getLargeCacheList(
                CacheConstants.BATCH_OPERATION_ERROR_RECORD + batchNo, batchOperationConfig.getRedisBatchSize());

        return ObjectUtils.isEmpty(errorList) ? new ArrayList<>() : errorList;
    }

    @Override
    public void validateBatchOperationRequest(BatchOperationRequestDTO request) {
        // 验证批量退回操作的前置状态
        request.validateBatchReturnSourceStatus();

        // 验证交付单数量限制
        if (request.getOrderCount() > batchOperationConfig.getMaxBatchSize()) {
            throw new IllegalArgumentException("单次批量操作最多支持" + batchOperationConfig.getMaxBatchSize() + "条记录");
        }

        // 验证交付单编号格式
        for (String orderNo : request.getDeliveryOrderNos()) {
            if (orderNo == null || orderNo.trim().isEmpty()) {
                throw new IllegalArgumentException("交付单编号不能为空");
            }
        }
    }

    @Override
    public String determineBatchReturnTargetStatus(String sourceStatus) {
        if (sourceStatus == null || sourceStatus.trim().isEmpty()) {
            throw new IllegalArgumentException("前置状态不能为空");
        }

        ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(sourceStatus);
        if (currentStatus == null) {
            throw new IllegalArgumentException("无效的前置状态: " + sourceStatus);
        }

        // 定义退回规则映射
        Map<ValueAddedDeliveryOrderStatus, ValueAddedDeliveryOrderStatus> returnMapping = new HashMap<>();
        returnMapping.put(ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED, ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION);
        returnMapping.put(ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION, ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION);
        returnMapping.put(ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION, ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY);
        returnMapping.put(ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY, ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT);

        ValueAddedDeliveryOrderStatus targetStatus = returnMapping.get(currentStatus);
        if (targetStatus == null) {
            throw new IllegalArgumentException("状态 " + currentStatus.getDescription() + " 不支持退回操作");
        }

        return targetStatus.getCode();
    }

    /**
     * 批量查询交付单
     */
    private List<ValueAddedDeliveryOrder> batchQueryOrders(List<String> deliveryOrderNos) {
        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ValueAddedDeliveryOrder::getDeliveryOrderNo, deliveryOrderNos);
        queryWrapper.eq(ValueAddedDeliveryOrder::getIsDel, false);

        List<ValueAddedDeliveryOrder> orders = valueAddedDeliveryOrderService.list(queryWrapper);

        // 检查是否有交付单不存在
        Set<String> foundOrderNos = orders.stream()
                .map(ValueAddedDeliveryOrder::getDeliveryOrderNo)
                .collect(Collectors.toSet());

        List<String> notFoundOrderNos = deliveryOrderNos.stream()
                .filter(orderNo -> !foundOrderNos.contains(orderNo))
                .collect(Collectors.toList());

        if (!notFoundOrderNos.isEmpty()) {
            throw new IllegalArgumentException("以下交付单不存在: " + String.join(", ", notFoundOrderNos));
        }

        return orders;
    }

    /**
     * 处理批量操作
     */
    private BatchOperationResult processBatchOperation(List<ValueAddedDeliveryOrder> orders,
                                                     BatchOperationRequestDTO request) {
        List<BatchOperationErrorDTO> errors = new ArrayList<>();
        List<String> successOrderNos = new ArrayList<>();

        for (ValueAddedDeliveryOrder order : orders) {
            try {
                // 确定目标状态
                String targetStatus = determineTargetStatus(order, request);

                // 构建状态变更请求
                StatusChangeRequestDTO changeRequest = buildStatusChangeRequest(order, targetStatus, request);

                // 执行状态变更
                valueAddedDeliveryOrderService.changeStatus(changeRequest);

                successOrderNos.add(order.getDeliveryOrderNo());

                log.debug("Batch operation success for order: {} from {} to {}",
                        order.getDeliveryOrderNo(), order.getStatus(), targetStatus);

            } catch (Exception e) {
                log.warn("Batch operation failed for order: {}, error: {}",
                        order.getDeliveryOrderNo(), e.getMessage());

                errors.add(buildErrorData(order, request, e.getMessage()));
            }
        }

        return new BatchOperationResult(successOrderNos, errors);
    }

    /**
     * 确定目标状态
     */
    private String determineTargetStatus(ValueAddedDeliveryOrder order, BatchOperationRequestDTO request) {
        if (request.getOperationType().isBatchReturn()) {
            // 批量退回需要动态确定目标状态
            return determineBatchReturnTargetStatus(request.getSourceStatus());
        } else {
            // 其他操作类型有固定的目标状态
            ValueAddedDeliveryOrderStatus targetStatus = request.getOperationType().getTargetStatus();
            if (targetStatus == null) {
                throw new IllegalArgumentException("操作类型 " + request.getOperationType().getDescription() + " 的目标状态未定义");
            }
            return targetStatus.getCode();
        }
    }

    /**
     * 构建状态变更请求
     */
    private StatusChangeRequestDTO buildStatusChangeRequest(ValueAddedDeliveryOrder order,
                                                          String targetStatus,
                                                          BatchOperationRequestDTO request) {
        return StatusChangeRequestDTO.builder()
                .deliveryOrderNo(order.getDeliveryOrderNo())
                .targetStatus(targetStatus)
                .reason(request.getReason())
                .operatorId(request.getOperatorId())
                .operatorName(request.getOperatorName())
                .remark(request.getRemark())
                .businessTopDeptId(request.getBusinessTopDeptId())
                .creditCode(order.getCreditCode())
                .build();
    }

    /**
     * 构建错误数据
     */
    private BatchOperationErrorDTO buildErrorData(ValueAddedDeliveryOrder order,
                                                BatchOperationRequestDTO request,
                                                String errorMessage) {
        String targetStatus = null;
        try {
            targetStatus = determineTargetStatus(order, request);
        } catch (Exception e) {
            targetStatus = "无法确定";
        }

        return BatchOperationErrorDTO.createError(
                order.getDeliveryOrderNo(),
                order.getCreditCode(),
                order.getBusinessTopDeptId(),
                order.getCustomerName(),
                order.getTitle(),
                order.getStatus(),
                targetStatus,
                request.getOperationType().getDescription(),
                errorMessage,
                request.getOperatorName()
        );
    }

    /**
     * 缓存异常数据
     */
    private void cacheErrorData(String batchNo, List<BatchOperationErrorDTO> errors) {
        try {
            redisService.setLargeCacheList(
                    CacheConstants.BATCH_OPERATION_ERROR_RECORD + batchNo,
                    errors, batchOperationConfig.getRedisBatchSize(),
                    batchOperationConfig.getErrorDataCacheTimeSeconds(), TimeUnit.SECONDS
            );
            log.info("Cached {} error records with batchNo: {}", errors.size(), batchNo);
        } catch (Exception e) {
            log.error("Failed to cache error data for batchNo: {}", batchNo, e);
        }
    }

    @Override
    public BatchOperationResultDTO executeBatchDelete(List<String> deliveryOrderNos,
                                                     Long operatorId,
                                                     String operatorName,
                                                     String reason) {
        log.info("Starting batch delete operation, order count: {}", deliveryOrderNos.size());

        long startTime = System.currentTimeMillis();
        String startTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 验证参数
        validateBatchDeleteRequest(deliveryOrderNos, operatorId);

        // 批量查询交付单
        List<ValueAddedDeliveryOrder> orders = batchQueryOrdersForDelete(deliveryOrderNos);

        // 执行批量删除
        BatchOperationResult result = processBatchDelete(orders, operatorId, operatorName, reason);

        // 缓存异常数据
        String batchNo = null;
        if (!result.getErrors().isEmpty()) {
            batchNo = UUID.randomUUID().toString().replaceAll("-", "");
            cacheErrorData(batchNo, result.getErrors());
        }

        long endTime = System.currentTimeMillis();
        String endTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 构建返回结果
        BatchOperationResultDTO resultDTO = BatchOperationResultDTO.builder()
                .batchNo(batchNo)
                .operationDescription("批量删除")
                .totalCount(orders.size())
                .successCount(result.getSuccessOrderNos().size())
                .errorCount(result.getErrors().size())
                .successOrderNos(result.getSuccessOrderNos())
                .errorOrderNos(result.getErrors().stream()
                        .map(BatchOperationErrorDTO::getDeliveryOrderNo)
                        .collect(Collectors.toList()))
                .startTime(startTimeStr)
                .endTime(endTimeStr)
                .duration(endTime - startTime)
                .hasErrors(!result.getErrors().isEmpty())
                .build();

        log.info("Batch delete operation completed: total: {}, success: {}, error: {}, duration: {}ms",
                resultDTO.getTotalCount(), resultDTO.getSuccessCount(),
                resultDTO.getErrorCount(), resultDTO.getDuration());

        return resultDTO;
    }

    /**
     * 验证批量删除请求
     */
    private void validateBatchDeleteRequest(List<String> deliveryOrderNos, Long operatorId) {
        if (deliveryOrderNos == null || deliveryOrderNos.isEmpty()) {
            throw new IllegalArgumentException("交付单编号列表不能为空");
        }

        if (deliveryOrderNos.size() > batchOperationConfig.getMaxBatchSize()) {
            throw new IllegalArgumentException("单次批量删除最多支持" + batchOperationConfig.getMaxBatchSize() + "条记录");
        }

        if (operatorId == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }

        // 验证交付单编号格式
        for (String orderNo : deliveryOrderNos) {
            if (orderNo == null || orderNo.trim().isEmpty()) {
                throw new IllegalArgumentException("交付单编号不能为空");
            }
        }
    }

    /**
     * 批量查询交付单（用于删除）
     */
    private List<ValueAddedDeliveryOrder> batchQueryOrdersForDelete(List<String> deliveryOrderNos) {
        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ValueAddedDeliveryOrder::getDeliveryOrderNo, deliveryOrderNos);
        queryWrapper.eq(ValueAddedDeliveryOrder::getIsDel, false); // 只查询未删除的记录

        List<ValueAddedDeliveryOrder> orders = valueAddedDeliveryOrderService.list(queryWrapper);

        // 检查是否有交付单不存在
        Set<String> foundOrderNos = orders.stream()
                .map(ValueAddedDeliveryOrder::getDeliveryOrderNo)
                .collect(Collectors.toSet());

        List<String> notFoundOrderNos = deliveryOrderNos.stream()
                .filter(orderNo -> !foundOrderNos.contains(orderNo))
                .collect(Collectors.toList());

        if (!notFoundOrderNos.isEmpty()) {
            throw new IllegalArgumentException("以下交付单不存在或已被删除: " + String.join(", ", notFoundOrderNos));
        }

        return orders;
    }

    /**
     * 处理批量删除
     */
    private BatchOperationResult processBatchDelete(List<ValueAddedDeliveryOrder> orders,
                                                   Long operatorId,
                                                   String operatorName,
                                                   String reason) {
        List<BatchOperationErrorDTO> errors = new ArrayList<>();
        List<String> successOrderNos = new ArrayList<>();

        // 定义允许删除的状态
        Set<String> allowedDeleteStatuses = new HashSet<>();
        allowedDeleteStatuses.add(ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED.getCode());    // 已关闭交付
        allowedDeleteStatuses.add(ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED.getCode()); // 已扣款
        allowedDeleteStatuses.add(ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED.getCode());     // 已关闭扣款

        for (ValueAddedDeliveryOrder order : orders) {
            try {
                // 验证状态是否允许删除
                if (!allowedDeleteStatuses.contains(order.getStatus())) {
                    throw new IllegalArgumentException("交付单状态不允许删除，当前状态: " +
                            ValueAddedDeliveryOrderStatus.getByCode(order.getStatus()).getDescription());
                }

                // 执行逻辑删除
                order.setIsDel(true);
                order.setUpdateBy(operatorId.toString());
                order.setUpdateTime(LocalDateTime.now());

                boolean updated = valueAddedDeliveryOrderService.updateById(order);
                if (!updated) {
                    throw new RuntimeException("删除失败，数据可能已被其他用户修改");
                }

                successOrderNos.add(order.getDeliveryOrderNo());

                log.debug("Batch delete success for order: {}, status: {}",
                        order.getDeliveryOrderNo(), order.getStatus());

            } catch (Exception e) {
                log.warn("Batch delete failed for order: {}, error: {}",
                        order.getDeliveryOrderNo(), e.getMessage());

                errors.add(buildDeleteErrorData(order, e.getMessage(), operatorName));
            }
        }

        return new BatchOperationResult(successOrderNos, errors);
    }

    /**
     * 构建删除错误数据
     */
    private BatchOperationErrorDTO buildDeleteErrorData(ValueAddedDeliveryOrder order,
                                                       String errorMessage,
                                                       String operatorName) {
        return BatchOperationErrorDTO.createError(
                order.getDeliveryOrderNo(),
                order.getCreditCode(),
                order.getBusinessTopDeptId(),
                order.getCustomerName(),
                order.getTitle(),
                order.getStatus(),
                "已删除",
                "批量删除",
                errorMessage,
                operatorName
        );
    }

    /**
     * 批量操作结果内部类
     */
    private static class BatchOperationResult {
        private final List<String> successOrderNos;
        private final List<BatchOperationErrorDTO> errors;

        public BatchOperationResult(List<String> successOrderNos, List<BatchOperationErrorDTO> errors) {
            this.successOrderNos = successOrderNos;
            this.errors = errors;
        }

        public List<String> getSuccessOrderNos() {
            return successOrderNos;
        }

        public List<BatchOperationErrorDTO> getErrors() {
            return errors;
        }
    }

    @Override
    public BatchExportTemplateResult batchExportImportOperationTemplate(BatchExportTemplateRequest request) {
        try {
            log.info("Batch export operation template request: operation={}, order count={}",
                    request.getOperationDescription(), request.getOrderCount());

            // 参数验证
            if (!request.isValidOperation()) {
                throw new IllegalArgumentException("无效的操作类型: " + request.getOperation());
            }

            // 批量查询交付单数据
            List<ValueAddedDeliveryOrder> orders = batchQueryOrders(request.getDeliveryOrderNoList());

            if (orders.isEmpty()) {
                throw new RuntimeException("未找到任何有效的交付单数据");
            }

            // 根据操作类型准备导出数据
            String fileName = generateFileName(request.getOperationDescription());
            BatchExportTemplateResult result;

            switch (request.getOperation()) {
                case DELIVERY:
                    result = prepareDeliveryTemplateData(orders, fileName);
                    break;
                case SUPPLEMENT_DELIVERY:
                    result = prepareSupplementDeliveryTemplateData(orders, fileName);
                    break;
                case DEDUCTION:
                    result = prepareDeductionTemplateData(orders, fileName);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的操作类型: " + request.getOperation());
            }

            log.info("Batch export operation template success: operation={}, exported count={}",
                    request.getOperationDescription(), result.getRecordCount());

            return result;

        } catch (IllegalArgumentException e) {
            log.warn("Batch export operation template validation failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Batch export operation template failed: operation={}",
                    request != null ? request.getOperationDescription() : "unknown", e);
            throw new RuntimeException("批量导出操作模板失败: " + e.getMessage());
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String operation) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return operation + "操作模板_" + timestamp + ".xlsx";
    }

    /**
     * 准备交付操作模板数据
     */
    private BatchExportTemplateResult prepareDeliveryTemplateData(List<ValueAddedDeliveryOrder> orders, String fileName) {
        // 转换为导出DTO
        List<DeliveryExportTemplateDTO> exportData = orders.stream()
                .map(this::convertToDeliveryExportDTO)
                .collect(Collectors.toList());

        return BatchExportTemplateResult.createDeliveryResult(exportData, fileName);
    }

    /**
     * 准备补充交付附件操作模板数据
     */
    private BatchExportTemplateResult prepareSupplementDeliveryTemplateData(List<ValueAddedDeliveryOrder> orders, String fileName) {
        // 转换为导出DTO
        List<SupplementDeliveryExportTemplateDTO> exportData = orders.stream()
                .map(this::convertToSupplementDeliveryExportDTO)
                .collect(Collectors.toList());

        return BatchExportTemplateResult.createSupplementDeliveryResult(exportData, fileName);
    }

    /**
     * 准备扣款操作模板数据
     */
    private BatchExportTemplateResult prepareDeductionTemplateData(List<ValueAddedDeliveryOrder> orders, String fileName) {
        // 转换为导出DTO
        List<DeductionExportTemplateDTO> exportData = orders.stream()
                .map(this::convertToDeductionExportDTO)
                .collect(Collectors.toList());

        return BatchExportTemplateResult.createDeductionResult(exportData, fileName);
    }

    /**
     * 转换为交付导出DTO
     */
    private DeliveryExportTemplateDTO convertToDeliveryExportDTO(ValueAddedDeliveryOrder order) {
        return DeliveryExportTemplateDTO.builder()
                .deliveryOrderNo(order.getDeliveryOrderNo())
                .customerName(order.getCustomerName())
                .creditCode(order.getCreditCode())
                .status(getStatusDescription(order.getStatus()))
                .totalWithholdingAmount(order.getTotalWithholdingAmount())
                .remark("") // 模板中为空，供用户填写
                .attachmentFileName("") // 模板中为空，供用户填写
                .attachmentFolderName("") // 模板中为空，供用户填写
                .stockFileName("") // 模板中为空，供用户填写
                .build();
    }

    /**
     * 转换为补充交付附件导出DTO
     */
    private SupplementDeliveryExportTemplateDTO convertToSupplementDeliveryExportDTO(ValueAddedDeliveryOrder order) {
        return SupplementDeliveryExportTemplateDTO.builder()
                .deliveryOrderNo(order.getDeliveryOrderNo())
                .customerName(order.getCustomerName())
                .creditCode(order.getCreditCode())
                .remark("") // 模板中为空，供用户填写
                .attachmentFileName("") // 模板中为空，供用户填写
                .attachmentFolderName("") // 模板中为空，供用户填写
                .stockFileName("") // 模板中为空，供用户填写
                .build();
    }

    /**
     * 转换为扣款导出DTO
     */
    private DeductionExportTemplateDTO convertToDeductionExportDTO(ValueAddedDeliveryOrder order) {
        return DeductionExportTemplateDTO.builder()
                .deliveryOrderNo(order.getDeliveryOrderNo())
                .customerName(order.getCustomerName())
                .creditCode(order.getCreditCode())
                .status(getStatusDescription(order.getStatus()))
                .remark("") // 模板中为空，供用户填写
                .attachmentFileName("") // 模板中为空，供用户填写
                .attachmentFolderName("") // 模板中为空，供用户填写
                .stockFileName("") // 模板中为空，供用户填写
                .build();
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(String statusCode) {
        if (StringUtils.isEmpty(statusCode)) {
            return "";
        }

        ValueAddedDeliveryOrderStatus status = ValueAddedDeliveryOrderStatus.getByCode(statusCode);
        return status != null ? status.getDescription() : statusCode;
    }
}
