package com.bxm.customer.domain.dto.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量导出操作模板请求
 *
 * 用于批量导出不同操作类型的Excel模板
 * 支持交付、补充交付附件、扣款三种操作类型的模板导出
 *
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量导出操作模板请求")
public class BatchExportTemplateRequest {

    /**
     * 交付单编号列表
     */
    @NotEmpty(message = "交付单编号列表不能为空")
    @Size(max = 1000, message = "单次导出最多支持1000条记录")
    @ApiModelProperty(value = "交付单编号列表", required = true, 
                     example = "[\"SW2508051430001A1C\", \"SW2508051430002A1C\"]")
    private List<String> deliveryOrderNoList;

    /**
     * 操作类型：交付、补充交付附件、扣款
     */
    @NotNull(message = "操作类型不能为空")
    @Pattern(regexp = "^(交付|补充交付附件|扣款)$", message = "操作类型只能是：交付、补充交付附件、扣款")
    @ApiModelProperty(value = "操作类型", required = true, 
                     allowableValues = "交付,补充交付附件,扣款",
                     example = "交付")
    private String operation;

    /**
     * 获取交付单数量
     */
    public int getOrderCount() {
        return deliveryOrderNoList == null ? 0 : deliveryOrderNoList.size();
    }

    /**
     * 验证操作类型是否有效
     */
    public boolean isValidOperation() {
        return operation != null && 
               (operation.equals("交付") || operation.equals("补充交付附件") || operation.equals("扣款"));
    }
}
