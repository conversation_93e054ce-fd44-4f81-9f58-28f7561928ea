package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.customer.domain.enums.ExportOperationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量导出模板结果
 *
 * 封装批量导出操作的结果数据，包含导出数据列表、文件名、操作类型等信息
 * 用于在服务层和控制层之间传递导出数据
 *
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量导出模板结果")
public class BatchExportTemplateResult {

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private ExportOperationType operationType;

    /**
     * 导出数据列表（泛型，根据操作类型确定具体类型）
     */
    @ApiModelProperty(value = "导出数据列表")
    private List<?> exportData;

    /**
     * 导出数据的Class类型
     */
    @ApiModelProperty(value = "导出数据的Class类型")
    private Class<?> dataClass;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 工作表名称
     */
    @ApiModelProperty(value = "工作表名称")
    private String sheetName;

    /**
     * 导出记录数量
     */
    @ApiModelProperty(value = "导出记录数量")
    private Integer recordCount;

    /**
     * 获取导出记录数量
     */
    public Integer getRecordCount() {
        return exportData != null ? exportData.size() : 0;
    }

    /**
     * 创建交付操作导出结果
     */
    public static BatchExportTemplateResult createDeliveryResult(List<DeliveryExportTemplateDTO> data, String fileName) {
        return BatchExportTemplateResult.builder()
                .operationType(ExportOperationType.DELIVERY)
                .exportData(data)
                .dataClass(DeliveryExportTemplateDTO.class)
                .fileName(fileName)
                .sheetName("交付操作模板")
                .build();
    }

    /**
     * 创建补充交付附件操作导出结果
     */
    public static BatchExportTemplateResult createSupplementDeliveryResult(List<SupplementDeliveryExportTemplateDTO> data, String fileName) {
        return BatchExportTemplateResult.builder()
                .operationType(ExportOperationType.SUPPLEMENT_DELIVERY)
                .exportData(data)
                .dataClass(SupplementDeliveryExportTemplateDTO.class)
                .fileName(fileName)
                .sheetName("补充交付附件操作模板")
                .build();
    }

    /**
     * 创建扣款操作导出结果
     */
    public static BatchExportTemplateResult createDeductionResult(List<DeductionExportTemplateDTO> data, String fileName) {
        return BatchExportTemplateResult.builder()
                .operationType(ExportOperationType.DEDUCTION)
                .exportData(data)
                .dataClass(DeductionExportTemplateDTO.class)
                .fileName(fileName)
                .sheetName("扣款操作模板")
                .build();
    }
}
